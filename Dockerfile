# 第一阶段：构建阶段
FROM cr.ttyuyin.com/public/node:18.16 as build-stage

WORKDIR /app
COPY . ./

# 设置 node 阿里镜像
RUN npm config set registry https://registry.npmmirror.com
# 设置--max-old-space-size
ENV NODE_OPTIONS=--max-old-space-size=16384

# 使用docker模式构建（包含运行时配置替换的占位符）
RUN npm install pnpm -g && \
    pnpm install --no-frozen-lockfile && \
    pnpm build:docker

# node部分结束
RUN echo "🎉 编 🎉 译 🎉 成 🎉 功 🎉"

# 第二阶段：运行阶段
FROM cr.ttyuyin.com/yunwei/nginx:1.19.5 as production-stage

# 复制构建产物到nginx目录
COPY --from=build-stage /app/dist /usr/share/nginx/html/dist

# 创建配置目录并复制环境配置文件
RUN mkdir -p /app/config
COPY .env.production /app/config/.env.production
COPY .env.development /app/config/.env.development
COPY .env.test /app/config/.env.test

# 复制nginx配置模板和脚本
COPY nginx.conf.template /etc/nginx/nginx.conf.template
COPY docker-entrypoint.sh /docker-entrypoint.sh
COPY config-replacer.js /usr/local/bin/config-replacer.js

# 安装node（用于运行配置替换脚本）
RUN apk add --no-cache nodejs npm

# 设置执行权限
RUN chmod +x /docker-entrypoint.sh

# 暴露端口
EXPOSE 80

# 使用自定义入口脚本
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
