#!/bin/bash

# 运行时配置替换脚本（Shell版本，无需Node.js）
# 根据环境变量动态替换前端应用中的配置

set -e

# 配置文件路径
DIST_PATH="/usr/share/nginx/html/dist"
CONFIG_PATH="/app/config"
APP_CONFIG_FILE="$DIST_PATH/_app.config.js"

# 环境配置映射
get_env_file() {
    case "$DEPLOY_ENV" in
        "production") echo ".env.production" ;;
        "development") echo ".env.development" ;;
        "test") echo ".env.test" ;;
        "staging") echo ".env.test" ;;  # staging 使用 test 配置
        *) echo ".env.production" ;;
    esac
}

# 解析环境配置文件
parse_env_file() {
    local env_file="$1"
    local config_file="$CONFIG_PATH/$env_file"
    
    if [ ! -f "$config_file" ]; then
        echo "⚠️  配置文件不存在: $config_file"
        return 1
    fi
    
    echo "📄 解析配置文件: $env_file"
    
    # 读取配置文件并设置变量
    while IFS='=' read -r key value; do
        # 跳过注释和空行
        [[ $key =~ ^[[:space:]]*# ]] && continue
        [[ -z "$key" ]] && continue
        
        # 移除前后空格
        key=$(echo "$key" | xargs)
        value=$(echo "$value" | xargs)
        
        # 移除引号
        value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
        
        # 导出变量
        if [[ $key =~ ^VITE_GLOB_ ]]; then
            export "$key=$value"
            echo "  $key=$value"
        fi
    done < "$config_file"
}

# 获取运行时配置
get_runtime_config() {
    local deploy_env="${DEPLOY_ENV:-production}"
    local env_file=$(get_env_file)

    echo "🔧 当前环境: $deploy_env"
    echo "📄 使用配置文件: $env_file"

    # 解析基础配置
    parse_env_file "$env_file"

    # 运行时环境变量覆盖（保持所有原有变量，只覆盖指定的）
    export VITE_GLOB_API_URL="${API_URL:-$VITE_GLOB_API_URL}"
    export VITE_GLOB_URL="${APP_URL:-$VITE_GLOB_URL}"
    export VITE_GLOB_SSO="${SSO_URL:-$VITE_GLOB_SSO}"
    export VITE_GLOB_CLIENT_ID="${CLIENT_ID:-$VITE_GLOB_CLIENT_ID}"
    export VITE_GLOB_UPLOAD_URL="${UPLOAD_URL:-$VITE_GLOB_UPLOAD_URL}"

    # 确保必需的变量有默认值
    export VITE_GLOB_APP_TITLE="${VITE_GLOB_APP_TITLE:-大禹 - 问题管理平台}"
    export VITE_GLOB_PUBLIC_PATH="${VITE_GLOB_PUBLIC_PATH:-/}"
    export VITE_GLOB_API_URL_PREFIX="${VITE_GLOB_API_URL_PREFIX:-}"

    echo "📋 最终配置:"
    echo "  APP_TITLE: $VITE_GLOB_APP_TITLE"
    echo "  API_URL: $VITE_GLOB_API_URL"
    echo "  APP_URL: $VITE_GLOB_URL"
    echo "  SSO_URL: $VITE_GLOB_SSO"
    echo "  CLIENT_ID: $VITE_GLOB_CLIENT_ID"
    echo "  UPLOAD_URL: $VITE_GLOB_UPLOAD_URL"
    echo "  PUBLIC_PATH: $VITE_GLOB_PUBLIC_PATH"
    echo "  API_URL_PREFIX: $VITE_GLOB_API_URL_PREFIX"
}

# 生成变量名（正确实现JavaScript版本的逻辑）
get_variable_name() {
    local title="$1"
    local hex=""

    # 使用Python来正确处理Unicode字符转换（如果可用）
    if command -v python3 >/dev/null 2>&1; then
        hex=$(python3 -c "
import sys
title = sys.argv[1]
result = []
for char in title:
    code = ord(char)
    hex_val = format(code, '04X')
    result.append(hex_val)
print(''.join(result))
" "$title")
    elif command -v python >/dev/null 2>&1; then
        hex=$(python -c "
import sys
title = sys.argv[1].decode('utf-8')
result = []
for char in title:
    code = ord(char)
    hex_val = format(code, '04X')
    result.append(hex_val)
print(''.join(result))
" "$title")
    else
        # 如果没有Python，使用固定的变量名
        echo "__PRODUCTION____APP__CONF__"
        return
    fi

    # 生成变量名格式：__PRODUCTION__[HEX]__CONF__
    echo "__PRODUCTION__${hex}__CONF__"
}

# 生成应用配置文件
generate_app_config() {
    local build_time="${BUILD_TIME:-$(date -u +"%Y-%m-%dT%H:%M:%SZ")}"
    local deploy_env="${DEPLOY_ENV:-production}"
    local app_version="${APP_VERSION:-latest}"

    # 生成正确的变量名
    local var_name=$(get_variable_name "$VITE_GLOB_APP_TITLE")

    echo "🔧 生成变量名: $var_name"

    # 生成配置文件内容
    cat > "$APP_CONFIG_FILE" << EOF
// 运行时生成的应用配置
(function () {
  window.__PRODUCTION__MODE__ = true;
  window.__APP_INFO__ = {
    "pkg": {
      "name": "itmp-frontend",
      "version": "$app_version"
    },
    "lastBuildTime": "$build_time",
    "deployEnv": "$deploy_env"
  };

  // 使用正确的变量名，包含所有VITE_GLOB_*变量
  window.$var_name = {
    "VITE_GLOB_APP_TITLE": "$VITE_GLOB_APP_TITLE",
    "VITE_GLOB_PUBLIC_PATH": "$VITE_GLOB_PUBLIC_PATH",
    "VITE_GLOB_API_URL": "$VITE_GLOB_API_URL",
    "VITE_GLOB_URL": "$VITE_GLOB_URL",
    "VITE_GLOB_SSO": "$VITE_GLOB_SSO",
    "VITE_GLOB_CLIENT_ID": "$VITE_GLOB_CLIENT_ID",
    "VITE_GLOB_UPLOAD_URL": "$VITE_GLOB_UPLOAD_URL",
    "VITE_GLOB_API_URL_PREFIX": "$VITE_GLOB_API_URL_PREFIX"
  };

  // 冻结对象防止修改
  Object.freeze(window.$var_name);
  Object.defineProperty(window, "$var_name", {
    configurable: false,
    writable: false,
  });

  console.log('🚀 应用配置已加载:', window.$var_name);
})();
EOF

    echo "✅ 应用配置文件生成完成: $APP_CONFIG_FILE"
}

# 替换HTML文件中的配置占位符
replace_html_placeholders() {
    local index_path="$DIST_PATH/index.html"

    if [ ! -f "$index_path" ]; then
        echo "⚠️  index.html 文件不存在"
        return 1
    fi

    echo "🔄 替换HTML占位符..."

    # 替换所有VITE_GLOB_*占位符
    sed -i "s|__vg_base_url|$VITE_GLOB_API_URL|g" "$index_path"
    sed -i "s|__VITE_GLOB_APP_TITLE__|$VITE_GLOB_APP_TITLE|g" "$index_path"
    sed -i "s|__VITE_GLOB_API_URL__|$VITE_GLOB_API_URL|g" "$index_path"
    sed -i "s|__VITE_GLOB_URL__|$VITE_GLOB_URL|g" "$index_path"
    sed -i "s|__VITE_GLOB_SSO__|$VITE_GLOB_SSO|g" "$index_path"
    sed -i "s|__VITE_GLOB_CLIENT_ID__|$VITE_GLOB_CLIENT_ID|g" "$index_path"
    sed -i "s|__VITE_GLOB_UPLOAD_URL__|$VITE_GLOB_UPLOAD_URL|g" "$index_path"
    sed -i "s|__VITE_GLOB_PUBLIC_PATH__|$VITE_GLOB_PUBLIC_PATH|g" "$index_path"
    sed -i "s|__VITE_GLOB_API_URL_PREFIX__|$VITE_GLOB_API_URL_PREFIX|g" "$index_path"

    echo "✅ HTML占位符替换完成"
}

# 替换JS文件中的配置占位符
replace_js_placeholders() {
    echo "🔄 替换JS文件占位符..."

    # 查找所有JS文件并替换占位符
    find "$DIST_PATH" -name "*.js" -type f | while read -r js_file; do
        local file_changed=false

        # 检查并替换各种占位符
        if grep -q "__vg_base_url\|__VITE_GLOB_" "$js_file" 2>/dev/null; then
            sed -i "s|__vg_base_url|$VITE_GLOB_API_URL|g" "$js_file"
            sed -i "s|__VITE_GLOB_APP_TITLE__|$VITE_GLOB_APP_TITLE|g" "$js_file"
            sed -i "s|__VITE_GLOB_API_URL__|$VITE_GLOB_API_URL|g" "$js_file"
            sed -i "s|__VITE_GLOB_URL__|$VITE_GLOB_URL|g" "$js_file"
            sed -i "s|__VITE_GLOB_SSO__|$VITE_GLOB_SSO|g" "$js_file"
            sed -i "s|__VITE_GLOB_CLIENT_ID__|$VITE_GLOB_CLIENT_ID|g" "$js_file"
            sed -i "s|__VITE_GLOB_UPLOAD_URL__|$VITE_GLOB_UPLOAD_URL|g" "$js_file"
            sed -i "s|__VITE_GLOB_PUBLIC_PATH__|$VITE_GLOB_PUBLIC_PATH|g" "$js_file"
            sed -i "s|__VITE_GLOB_API_URL_PREFIX__|$VITE_GLOB_API_URL_PREFIX|g" "$js_file"
            echo "  ✅ 替换JS文件: $(basename "$js_file")"
        fi
    done

    echo "✅ JS文件占位符替换完成"
}

# 主函数
main() {
    echo "🔄 开始运行时配置替换..."
    
    # 获取运行时配置
    get_runtime_config
    
    # 生成应用配置文件
    generate_app_config
    
    # 替换HTML占位符
    replace_html_placeholders
    
    # 替换JS文件占位符
    replace_js_placeholders
    
    echo "🎉 运行时配置替换完成!"
}

# 执行主函数
main "$@"
