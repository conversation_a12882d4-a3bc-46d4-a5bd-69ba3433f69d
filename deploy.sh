#!/bin/bash

# ITMP Frontend 运行时多环境部署脚本
# 使用方法: ./deploy.sh [环境] [版本]

set -e

# 默认参数
ENV=${1:-production}
VERSION=${2:-latest}
REGISTRY=${DOCKER_REGISTRY:-cr.ttyuyin.com/itmp}
IMAGE_NAME=${IMAGE_NAME:-itmp-frontend}
CONTAINER_NAME="itmp-frontend-${ENV}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 环境配置
get_env_config() {
    case "$ENV" in
        "development")
            PORT=3001
            API_URL="http://itmp-test.ttyuyin.com/api"
            APP_URL="http://localhost:3001"
            SSO_URL="test-ebc-sso.52tt.com"
            CLIENT_ID="itmp-dev"
            ;;
        "test"|"staging")
            PORT=3002
            API_URL="http://itmp-test.ttyuyin.com/api"
            APP_URL="http://test.itmp.ttyuyin.com"
            SSO_URL="test-ebc-sso.52tt.com"
            CLIENT_ID="itmp-test"
            ;;
        "production")
            PORT=80
            API_URL="https://itmp.ttyuyin.com/api"
            APP_URL="https://itmp.ttyuyin.com"
            SSO_URL="ebc-sso.52tt.com"
            CLIENT_ID="itmp"
            ;;
        *)
            print_error "不支持的环境: $ENV"
            print_info "支持的环境: development, test, staging, production"
            exit 1
            ;;
    esac
    
    # 允许环境变量覆盖
    API_URL=${OVERRIDE_API_URL:-$API_URL}
    APP_URL=${OVERRIDE_APP_URL:-$APP_URL}
    SSO_URL=${OVERRIDE_SSO_URL:-$SSO_URL}
    CLIENT_ID=${OVERRIDE_CLIENT_ID:-$CLIENT_ID}
    UPLOAD_URL=${OVERRIDE_UPLOAD_URL:-/upload}
}

# 停止并删除旧容器
cleanup_old_container() {
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        print_info "停止旧容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" || true
        docker rm "$CONTAINER_NAME" || true
        print_success "旧容器已清理"
    fi
}

# 拉取最新镜像
pull_image() {
    local image_tag="$REGISTRY/$IMAGE_NAME:$VERSION"
    print_info "拉取镜像: $image_tag"
    
    if docker pull "$image_tag"; then
        print_success "镜像拉取成功"
    else
        print_error "镜像拉取失败"
        exit 1
    fi
}

# 启动新容器
start_container() {
    local image_tag="$REGISTRY/$IMAGE_NAME:$VERSION"
    
    print_info "启动新容器: $CONTAINER_NAME"
    print_info "环境: $ENV"
    print_info "端口映射: $PORT:80"
    print_info "API地址: $API_URL"
    print_info "应用地址: $APP_URL"
    print_info "SSO地址: $SSO_URL"
    
    # 构建docker run命令
    local docker_cmd="docker run -d \
        --name $CONTAINER_NAME \
        --restart unless-stopped \
        -p $PORT:80 \
        -e DEPLOY_ENV=$ENV \
        -e APP_VERSION=$VERSION \
        -e BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ") \
        -e API_URL=$API_URL \
        -e APP_URL=$APP_URL \
        -e SSO_URL=$SSO_URL \
        -e CLIENT_ID=$CLIENT_ID \
        -e UPLOAD_URL=$UPLOAD_URL"
    
    # 如果有API代理URL，添加代理配置
    if [ ! -z "$API_PROXY_URL" ]; then
        docker_cmd="$docker_cmd -e API_PROXY_URL=$API_PROXY_URL"
    fi
    
    # 添加自定义环境变量
    if [ ! -z "$CUSTOM_ENV_VARS" ]; then
        docker_cmd="$docker_cmd $CUSTOM_ENV_VARS"
    fi
    
    docker_cmd="$docker_cmd $image_tag"
    
    if eval "$docker_cmd"; then
        print_success "容器启动成功"
    else
        print_error "容器启动失败"
        exit 1
    fi
}

# 健康检查
health_check() {
    print_info "等待服务启动..."
    sleep 15
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "http://localhost:$PORT/health" > /dev/null 2>&1; then
            print_success "健康检查通过"
            
            # 显示环境信息
            print_info "环境信息:"
            local env_info=$(curl -s "http://localhost:$PORT/env-info" 2>/dev/null || echo "{}")
            echo "$env_info" | jq . 2>/dev/null || echo "$env_info"
            
            # 验证环境配置
            if echo "$env_info" | grep -q "\"environment\": \"$ENV\""; then
                print_success "环境配置验证通过"
            else
                print_warning "环境配置可能有问题"
            fi
            
            return 0
        fi
        
        print_info "健康检查失败，重试 $attempt/$max_attempts"
        sleep 5
        ((attempt++))
    done
    
    print_error "健康检查超时失败"
    return 1
}

# 显示容器状态
show_status() {
    print_info "容器状态:"
    docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    print_info "容器日志 (最后10行):"
    docker logs --tail 10 "$CONTAINER_NAME"
    
    print_info "访问地址:"
    echo "  本地访问: http://localhost:$PORT"
    echo "  健康检查: http://localhost:$PORT/health"
    echo "  环境信息: http://localhost:$PORT/env-info"
    if [ "$ENV" != "production" ]; then
        echo "  配置信息: http://localhost:$PORT/config-info"
    fi
}

# 回滚功能
rollback() {
    print_warning "部署失败，开始回滚..."
    
    # 停止失败的容器
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
    
    # 尝试启动备份容器（如果存在）
    local backup_container="${CONTAINER_NAME}-backup"
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${backup_container}$"; then
        print_info "恢复备份容器: $backup_container"
        docker start "$backup_container"
        docker rename "$backup_container" "$CONTAINER_NAME"
        print_success "回滚完成"
    else
        print_error "没有找到备份容器，无法回滚"
    fi
}

# 创建备份
create_backup() {
    if docker ps --filter "name=$CONTAINER_NAME" --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        local backup_name="${CONTAINER_NAME}-backup"
        
        # 删除旧备份
        docker rm "$backup_name" 2>/dev/null || true
        
        # 创建新备份
        print_info "创建容器备份: $backup_name"
        docker rename "$CONTAINER_NAME" "$backup_name"
        docker stop "$backup_name"
    fi
}

# 主函数
main() {
    print_info "=== ITMP Frontend 运行时多环境部署脚本 ==="
    print_info "环境: $ENV"
    print_info "版本: $VERSION"
    print_info "镜像: $REGISTRY/$IMAGE_NAME:$VERSION"
    echo ""
    
    # 获取环境配置
    get_env_config
    
    # 创建备份
    create_backup
    
    # 清理旧容器
    cleanup_old_container
    
    # 拉取镜像
    pull_image
    
    # 启动容器
    start_container
    
    # 健康检查
    if health_check; then
        print_success "=== 部署成功 ==="
        show_status
        
        # 清理备份容器
        docker rm "${CONTAINER_NAME}-backup" 2>/dev/null || true
    else
        print_error "=== 部署失败 ==="
        rollback
        exit 1
    fi
}

# 显示帮助
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "ITMP Frontend 运行时多环境部署脚本"
    echo ""
    echo "特点:"
    echo "  - 使用同一个镜像部署到不同环境"
    echo "  - 运行时通过环境变量配置"
    echo "  - 支持蓝绿部署和回滚"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [版本]"
    echo ""
    echo "参数:"
    echo "  环境    部署环境 (development|test|staging|production) [默认: production]"
    echo "  版本    镜像版本 [默认: latest]"
    echo ""
    echo "环境变量覆盖:"
    echo "  OVERRIDE_API_URL     覆盖API地址"
    echo "  OVERRIDE_APP_URL     覆盖应用地址"
    echo "  OVERRIDE_SSO_URL     覆盖SSO地址"
    echo "  OVERRIDE_CLIENT_ID   覆盖客户端ID"
    echo "  API_PROXY_URL        启用API代理"
    echo "  CUSTOM_ENV_VARS      自定义环境变量"
    echo ""
    echo "示例:"
    echo "  $0 production v1.0.0                    # 部署生产环境"
    echo "  $0 test latest                          # 部署测试环境"
    echo "  OVERRIDE_API_URL=http://custom.api $0   # 使用自定义API地址"
    exit 0
fi

# 执行主函数
main "$@"
