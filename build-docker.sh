#!/bin/bash

# 单次构建多环境Docker镜像脚本
# 使用方法: ./build-docker.sh [镜像标签] [推送到仓库]
# 示例: ./build-docker.sh v1.0.0 true

set -e

# 默认参数
TAG=${1:-latest}
PUSH=${2:-false}
REGISTRY=${DOCKER_REGISTRY:-cr.ttyuyin.com/itmp}
IMAGE_NAME=${IMAGE_NAME:-itmp-frontend}
BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 构建镜像
build_image() {
    local full_tag="$REGISTRY/$IMAGE_NAME:$TAG"
    local latest_tag="$REGISTRY/$IMAGE_NAME:latest"
    
    print_info "开始构建通用镜像..."
    print_info "镜像标签: $full_tag"
    print_info "构建时间: $BUILD_TIME"
    
    # 构建镜像
    docker build \
        --build-arg BUILD_TIME="$BUILD_TIME" \
        -t "$full_tag" \
        -t "$latest_tag" \
        .
    
    print_success "镜像构建完成: $full_tag"
    
    # 显示镜像信息
    docker images | grep "$IMAGE_NAME" | head -5
}

# 推送镜像
push_image() {
    if [ "$PUSH" = "true" ]; then
        local full_tag="$REGISTRY/$IMAGE_NAME:$TAG"
        local latest_tag="$REGISTRY/$IMAGE_NAME:latest"
        
        print_info "推送镜像到仓库..."
        
        docker push "$full_tag"
        docker push "$latest_tag"
        
        print_success "镜像推送完成"
    else
        print_info "跳过镜像推送（设置第二个参数为 true 来启用推送）"
    fi
}

# 测试镜像（多环境）
test_image() {
    local full_tag="$REGISTRY/$IMAGE_NAME:$TAG"
    local base_port=8080
    
    print_info "测试镜像多环境支持..."
    
    # 测试环境列表
    local environments=("production" "development" "test")
    local test_containers=()
    
    for env in "${environments[@]}"; do
        local test_port=$((base_port + $(echo "$env" | wc -c)))
        local container_name="test-$IMAGE_NAME-$env-$$"
        test_containers+=("$container_name:$test_port")
        
        print_info "启动 $env 环境测试容器: $container_name (端口: $test_port)"
        
        # 启动测试容器
        docker run -d \
            --name "$container_name" \
            -p "$test_port:80" \
            -e DEPLOY_ENV="$env" \
            -e BUILD_TIME="$BUILD_TIME" \
            -e APP_VERSION="$TAG" \
            -e API_URL="http://api-$env.example.com" \
            "$full_tag"
    done
    
    # 等待容器启动
    sleep 10
    
    # 测试每个环境
    local all_passed=true
    for container_info in "${test_containers[@]}"; do
        local container_name="${container_info%:*}"
        local test_port="${container_info#*:}"
        local env_name="${container_name#*-$IMAGE_NAME-}"
        env_name="${env_name%-*}"
        
        print_info "测试 $env_name 环境 (端口: $test_port)..."
        
        # 健康检查
        if curl -f "http://localhost:$test_port/health" > /dev/null 2>&1; then
            print_success "$env_name 环境健康检查通过"
            
            # 检查环境信息
            local env_info=$(curl -s "http://localhost:$test_port/env-info" 2>/dev/null || echo "{}")
            echo "  环境信息: $env_info"
            
            # 验证环境变量
            if echo "$env_info" | grep -q "\"environment\": \"$env_name\""; then
                print_success "$env_name 环境配置正确"
            else
                print_error "$env_name 环境配置错误"
                all_passed=false
            fi
        else
            print_error "$env_name 环境健康检查失败"
            all_passed=false
        fi
    done
    
    # 清理测试容器
    print_info "清理测试容器..."
    for container_info in "${test_containers[@]}"; do
        local container_name="${container_info%:*}"
        docker stop "$container_name" > /dev/null 2>&1 || true
        docker rm "$container_name" > /dev/null 2>&1 || true
    done
    
    if [ "$all_passed" = true ]; then
        print_success "所有环境测试通过"
    else
        print_error "部分环境测试失败"
        return 1
    fi
}

# 显示使用帮助
show_help() {
    echo "单次构建多环境Docker镜像脚本"
    echo ""
    echo "特点:"
    echo "  - 只需构建一次镜像"
    echo "  - 运行时通过环境变量区分环境"
    echo "  - 支持 production、development、test 等环境"
    echo ""
    echo "使用方法:"
    echo "  $0 [标签] [是否推送]"
    echo ""
    echo "参数:"
    echo "  标签      镜像标签 [默认: latest]"
    echo "  是否推送  是否推送到仓库 (true|false) [默认: false]"
    echo ""
    echo "环境变量:"
    echo "  DOCKER_REGISTRY  Docker仓库地址 [默认: cr.ttyuyin.com/itmp]"
    echo "  IMAGE_NAME       镜像名称 [默认: itmp-frontend]"
    echo ""
    echo "示例:"
    echo "  $0 v1.0.0 true           # 构建 v1.0.0 版本并推送"
    echo "  $0 latest false          # 构建 latest 版本但不推送"
    echo "  $0                       # 构建 latest 版本（使用默认参数）"
    echo ""
    echo "运行示例:"
    echo "  # 生产环境"
    echo "  docker run -d -p 80:80 -e DEPLOY_ENV=production $REGISTRY/$IMAGE_NAME:$TAG"
    echo ""
    echo "  # 开发环境"
    echo "  docker run -d -p 3000:80 -e DEPLOY_ENV=development -e API_URL=http://dev-api.com $REGISTRY/$IMAGE_NAME:$TAG"
    echo ""
    echo "  # 测试环境"
    echo "  docker run -d -p 3001:80 -e DEPLOY_ENV=test -e API_URL=http://test-api.com $REGISTRY/$IMAGE_NAME:$TAG"
    echo ""
}

# 主函数
main() {
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    print_info "=== ITMP Frontend 通用Docker镜像构建脚本 ==="
    print_info "标签: $TAG"
    print_info "推送: $PUSH"
    print_info "仓库: $REGISTRY"
    print_info "镜像: $IMAGE_NAME"
    print_info "构建时间: $BUILD_TIME"
    echo ""
    
    build_image
    test_image
    push_image
    
    print_success "=== 构建流程完成 ==="
    
    # 显示运行命令示例
    echo ""
    print_info "运行命令示例:"
    echo ""
    echo "# 生产环境:"
    echo "docker run -d -p 80:80 \\"
    echo "  -e DEPLOY_ENV=production \\"
    echo "  -e API_URL=https://api.prod.com \\"
    echo "  $REGISTRY/$IMAGE_NAME:$TAG"
    echo ""
    echo "# 开发环境:"
    echo "docker run -d -p 3000:80 \\"
    echo "  -e DEPLOY_ENV=development \\"
    echo "  -e API_URL=http://api.dev.com \\"
    echo "  $REGISTRY/$IMAGE_NAME:$TAG"
    echo ""
    echo "# 测试环境:"
    echo "docker run -d -p 3001:80 \\"
    echo "  -e DEPLOY_ENV=test \\"
    echo "  -e API_URL=http://api.test.com \\"
    echo "  $REGISTRY/$IMAGE_NAME:$TAG"
}

# 执行主函数
main "$@"
