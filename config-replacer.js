#!/usr/bin/env node

/**
 * 运行时配置替换脚本
 * 根据环境变量动态替换前端应用中的配置
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const DIST_PATH = '/usr/share/nginx/html/dist';
const CONFIG_PATH = '/app/config';
const APP_CONFIG_FILE = path.join(DIST_PATH, '_app.config.js');

// 环境配置映射
const ENV_CONFIG_MAP = {
  production: '.env.production',
  development: '.env.development',
  test: '.env.test',
  staging: '.env.test' // staging 使用 test 配置
};

/**
 * 解析环境配置文件
 */
function parseEnvFile(envFile) {
  try {
    const content = fs.readFileSync(path.join(CONFIG_PATH, envFile), 'utf8');
    const config = {};
    
    content.split('\n').forEach(line => {
      line = line.trim();
      if (line && !line.startsWith('#') && line.includes('=')) {
        const [key, ...valueParts] = line.split('=');
        let value = valueParts.join('=').trim();
        
        // 移除引号
        if ((value.startsWith('"') && value.endsWith('"')) || 
            (value.startsWith("'") && value.endsWith("'"))) {
          value = value.slice(1, -1);
        }
        
        config[key.trim()] = value;
      }
    });
    
    return config;
  } catch (error) {
    console.error(`解析环境配置文件失败: ${envFile}`, error.message);
    return {};
  }
}

/**
 * 获取运行时环境配置
 */
function getRuntimeConfig() {
  const deployEnv = process.env.DEPLOY_ENV || 'production';
  const envFile = ENV_CONFIG_MAP[deployEnv] || ENV_CONFIG_MAP.production;
  
  console.log(`🔧 当前环境: ${deployEnv}`);
  console.log(`📄 使用配置文件: ${envFile}`);
  
  // 解析基础配置
  const baseConfig = parseEnvFile(envFile);
  
  // 运行时环境变量覆盖
  const runtimeConfig = {
    ...baseConfig,
    // 支持运行时覆盖的关键配置
    VITE_GLOB_API_URL: process.env.API_URL || baseConfig.VITE_GLOB_API_URL,
    VITE_GLOB_URL: process.env.APP_URL || baseConfig.VITE_GLOB_URL,
    VITE_GLOB_SSO: process.env.SSO_URL || baseConfig.VITE_GLOB_SSO,
    VITE_GLOB_CLIENT_ID: process.env.CLIENT_ID || baseConfig.VITE_GLOB_CLIENT_ID,
    VITE_GLOB_UPLOAD_URL: process.env.UPLOAD_URL || baseConfig.VITE_GLOB_UPLOAD_URL
  };
  
  // 显示最终配置
  console.log('📋 最终配置:');
  Object.entries(runtimeConfig).forEach(([key, value]) => {
    if (key.startsWith('VITE_GLOB_')) {
      console.log(`  ${key}: ${value}`);
    }
  });
  
  return runtimeConfig;
}

/**
 * 生成应用配置文件
 */
function generateAppConfig(config) {
  // 过滤出前端需要的配置
  const appConfig = {};
  Object.entries(config).forEach(([key, value]) => {
    if (key.startsWith('VITE_GLOB_')) {
      // 移除 VITE_GLOB_ 前缀
      const configKey = key.replace('VITE_GLOB_', '');
      appConfig[configKey] = value;
    }
  });
  
  // 添加运行时信息
  appConfig.BUILD_TIME = process.env.BUILD_TIME || new Date().toISOString();
  appConfig.DEPLOY_ENV = process.env.DEPLOY_ENV || 'production';
  appConfig.VERSION = process.env.APP_VERSION || 'latest';
  
  // 生成配置文件内容
  const configContent = `// 运行时生成的应用配置
(function () {
  window.__PRODUCTION__MODE__ = true;
  window.__APP_INFO__ = ${JSON.stringify({
    pkg: { name: 'itmp-frontend', version: appConfig.VERSION },
    lastBuildTime: appConfig.BUILD_TIME,
    deployEnv: appConfig.DEPLOY_ENV
  }, null, 2)};
  
  // 全局配置
  window._GLOBAL_CONFIG_ = ${JSON.stringify(appConfig, null, 2)};
  
  console.log('🚀 应用配置已加载:', window._GLOBAL_CONFIG_);
})();`;
  
  return configContent;
}

/**
 * 替换HTML文件中的配置占位符
 */
function replaceHtmlPlaceholders(config) {
  const indexPath = path.join(DIST_PATH, 'index.html');
  
  try {
    let htmlContent = fs.readFileSync(indexPath, 'utf8');
    
    // 替换占位符
    Object.entries(config).forEach(([key, value]) => {
      const placeholder = `__${key}__`;
      htmlContent = htmlContent.replace(new RegExp(placeholder, 'g'), value);
    });
    
    // 替换特殊占位符
    htmlContent = htmlContent.replace(/__vg_base_url/g, config.VITE_GLOB_API_URL || '/api');
    
    fs.writeFileSync(indexPath, htmlContent);
    console.log('✅ HTML占位符替换完成');
  } catch (error) {
    console.error('❌ HTML占位符替换失败:', error.message);
  }
}

/**
 * 替换JS文件中的配置占位符
 */
function replaceJsPlaceholders(config) {
  try {
    const files = fs.readdirSync(DIST_PATH);
    const jsFiles = files.filter(file => file.endsWith('.js'));
    
    jsFiles.forEach(file => {
      const filePath = path.join(DIST_PATH, file);
      let content = fs.readFileSync(filePath, 'utf8');
      
      // 替换API URL占位符
      if (content.includes('__vg_base_url')) {
        content = content.replace(/__vg_base_url/g, config.VITE_GLOB_API_URL || '/api');
        fs.writeFileSync(filePath, content);
        console.log(`✅ 替换JS文件占位符: ${file}`);
      }
    });
  } catch (error) {
    console.error('❌ JS文件占位符替换失败:', error.message);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔄 开始运行时配置替换...');
  
  try {
    // 获取运行时配置
    const config = getRuntimeConfig();
    
    // 生成应用配置文件
    const appConfigContent = generateAppConfig(config);
    fs.writeFileSync(APP_CONFIG_FILE, appConfigContent);
    console.log('✅ 应用配置文件生成完成');
    
    // 替换HTML占位符
    replaceHtmlPlaceholders(config);
    
    // 替换JS文件占位符
    replaceJsPlaceholders(config);
    
    console.log('🎉 运行时配置替换完成!');
  } catch (error) {
    console.error('❌ 配置替换失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  main,
  getRuntimeConfig,
  generateAppConfig
};
