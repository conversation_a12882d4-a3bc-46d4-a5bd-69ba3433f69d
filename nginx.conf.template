#user  nobody;
worker_processes auto;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '"env:${DEPLOY_ENV}" "version:${APP_VERSION}"';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    server {
        listen 80;
        server_name _;
        root /usr/share/nginx/html/dist;
        index index.html;
        
        # 环境标识头（用于调试）
        add_header X-Deploy-Env "${DEPLOY_ENV}" always;
        add_header X-App-Version "${APP_VERSION}" always;
        add_header X-Build-Time "${BUILD_TIME}" always;
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        # CORS配置
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        
        # 静态资源缓存策略（根据环境调整）
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            # 根据环境设置不同的缓存策略
            set $cache_time "31536000"; # 默认1年
            if ($http_x_deploy_env ~* "^(development|test)$") {
                set $cache_time "3600"; # 开发/测试环境1小时
            }
            expires ${cache_time}s;
            add_header Cache-Control "public, max-age=${cache_time}";
            try_files $uri =404;
        }
        
        # 应用配置文件（运行时生成）
        location = /_app.config.js {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            try_files $uri =404;
        }
        
        # HTML文件不缓存
        location ~* \.(html|htm)$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            try_files $uri $uri/ /index.html;
        }
        
        # 主路由
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # API代理配置（根据环境变量动态配置）
        location /api/ {
            # 这里会在运行时被替换为实际的代理配置
            # PROXY_CONFIG_PLACEHOLDER
            try_files $uri $uri/ /index.html;
        }
        
        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy - env: ${DEPLOY_ENV}, version: ${APP_VERSION}\n";
            add_header Content-Type text/plain;
        }
        
        # 环境信息接口
        location /env-info {
            access_log off;
            return 200 '{"environment": "${DEPLOY_ENV}", "version": "${APP_VERSION}", "buildTime": "${BUILD_TIME}"}';
            add_header Content-Type application/json;
        }
        
        # 配置信息接口（仅非生产环境）
        location /config-info {
            # 仅在非生产环境提供配置信息
            if ($http_x_deploy_env = "production") {
                return 404;
            }
            access_log off;
            alias /usr/share/nginx/html/dist/_app.config.js;
            add_header Content-Type application/javascript;
        }
    }
}
