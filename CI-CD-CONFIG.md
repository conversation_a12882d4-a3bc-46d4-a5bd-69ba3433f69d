# CI/CD 平台配置指南

本项目支持一次构建，多环境部署的Docker方案，适配各种CI/CD平台。

## 🏗️ 构建配置

### Dockerfile 说明
项目使用单一Dockerfile，构建包含所有环境配置的通用镜像：

```dockerfile
# 构建阶段：使用 pnpm build:docker
RUN pnpm build:docker

# 运行阶段：包含所有环境配置文件
COPY .env.production /app/config/.env.production
COPY .env.development /app/config/.env.development  
COPY .env.test /app/config/.env.test
```

### 构建命令
```bash
# CI/CD平台构建命令
docker build -t ${IMAGE_NAME}:${BUILD_TAG} .
```

### 构建参数
无需构建参数，所有配置在运行时确定。

## 🚀 部署配置

### 环境变量配置

#### 必需环境变量
- `DEPLOY_ENV`: 部署环境 (development/test/staging/production)

#### 可选环境变量
- `API_URL`: API服务地址
- `APP_URL`: 应用访问地址  
- `SSO_URL`: SSO服务地址
- `CLIENT_ID`: 客户端ID
- `UPLOAD_URL`: 文件上传地址
- `APP_VERSION`: 应用版本号

### 不同环境的部署配置

#### 开发环境
```yaml
environment:
  DEPLOY_ENV: development
  API_URL: http://itmp-test.ttyuyin.com/api
  APP_URL: http://dev.itmp.ttyuyin.com
  SSO_URL: test-ebc-sso.52tt.com
  CLIENT_ID: itmp-dev
ports:
  - "3001:80"
```

#### 测试环境
```yaml
environment:
  DEPLOY_ENV: test
  API_URL: http://itmp-test.ttyuyin.com/api
  APP_URL: http://test.itmp.ttyuyin.com
  SSO_URL: test-ebc-sso.52tt.com
  CLIENT_ID: itmp-test
ports:
  - "3002:80"
```

#### 生产环境
```yaml
environment:
  DEPLOY_ENV: production
  API_URL: https://itmp.ttyuyin.com/api
  APP_URL: https://itmp.ttyuyin.com
  SSO_URL: ebc-sso.52tt.com
  CLIENT_ID: itmp
ports:
  - "80:80"
```

## 📋 CI/CD 平台配置示例

### Jenkins Pipeline
```groovy
pipeline {
    agent any
    
    environment {
        IMAGE_NAME = 'cr.ttyuyin.com/itmp/itmp-frontend'
        BUILD_TAG = "${env.BUILD_NUMBER}"
    }
    
    stages {
        stage('Build') {
            steps {
                script {
                    docker.build("${IMAGE_NAME}:${BUILD_TAG}")
                    docker.build("${IMAGE_NAME}:latest")
                }
            }
        }
        
        stage('Push') {
            steps {
                script {
                    docker.withRegistry('https://cr.ttyuyin.com', 'harbor-credentials') {
                        docker.image("${IMAGE_NAME}:${BUILD_TAG}").push()
                        docker.image("${IMAGE_NAME}:latest").push()
                    }
                }
            }
        }
        
        stage('Deploy to Test') {
            steps {
                sh '''
                    docker run -d \
                        --name itmp-frontend-test-${BUILD_TAG} \
                        -p 3002:80 \
                        -e DEPLOY_ENV=test \
                        -e API_URL=http://itmp-test.ttyuyin.com/api \
                        -e APP_VERSION=${BUILD_TAG} \
                        ${IMAGE_NAME}:${BUILD_TAG}
                '''
            }
        }
        
        stage('Deploy to Production') {
            when {
                branch 'main'
            }
            steps {
                sh '''
                    docker run -d \
                        --name itmp-frontend-prod-${BUILD_TAG} \
                        -p 80:80 \
                        -e DEPLOY_ENV=production \
                        -e API_URL=https://itmp.ttyuyin.com/api \
                        -e APP_VERSION=${BUILD_TAG} \
                        ${IMAGE_NAME}:${BUILD_TAG}
                '''
            }
        }
    }
}
```

### GitLab CI
```yaml
# .gitlab-ci.yml
stages:
  - build
  - deploy-test
  - deploy-prod

variables:
  IMAGE_NAME: cr.ttyuyin.com/itmp/itmp-frontend
  DOCKER_DRIVER: overlay2

build:
  stage: build
  script:
    - docker build -t $IMAGE_NAME:$CI_COMMIT_SHA .
    - docker tag $IMAGE_NAME:$CI_COMMIT_SHA $IMAGE_NAME:latest
    - docker push $IMAGE_NAME:$CI_COMMIT_SHA
    - docker push $IMAGE_NAME:latest

deploy-test:
  stage: deploy-test
  script:
    - |
      docker run -d \
        --name itmp-frontend-test-$CI_COMMIT_SHA \
        -p 3002:80 \
        -e DEPLOY_ENV=test \
        -e API_URL=http://itmp-test.ttyuyin.com/api \
        -e APP_VERSION=$CI_COMMIT_SHA \
        $IMAGE_NAME:$CI_COMMIT_SHA

deploy-prod:
  stage: deploy-prod
  script:
    - |
      docker run -d \
        --name itmp-frontend-prod-$CI_COMMIT_SHA \
        -p 80:80 \
        -e DEPLOY_ENV=production \
        -e API_URL=https://itmp.ttyuyin.com/api \
        -e APP_VERSION=$CI_COMMIT_SHA \
        $IMAGE_NAME:$CI_COMMIT_SHA
  only:
    - main
```

### Kubernetes 部署

#### 开发环境
```yaml
# k8s-dev.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: itmp-frontend-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: itmp-frontend-dev
  template:
    metadata:
      labels:
        app: itmp-frontend-dev
    spec:
      containers:
      - name: itmp-frontend
        image: cr.ttyuyin.com/itmp/itmp-frontend:latest
        ports:
        - containerPort: 80
        env:
        - name: DEPLOY_ENV
          value: "development"
        - name: API_URL
          value: "http://itmp-test.ttyuyin.com/api"
        - name: APP_URL
          value: "http://dev.itmp.ttyuyin.com"
        - name: SSO_URL
          value: "test-ebc-sso.52tt.com"
        - name: CLIENT_ID
          value: "itmp-dev"
---
apiVersion: v1
kind: Service
metadata:
  name: itmp-frontend-dev-service
spec:
  selector:
    app: itmp-frontend-dev
  ports:
  - port: 80
    targetPort: 80
  type: LoadBalancer
```

#### 生产环境
```yaml
# k8s-prod.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: itmp-frontend-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: itmp-frontend-prod
  template:
    metadata:
      labels:
        app: itmp-frontend-prod
    spec:
      containers:
      - name: itmp-frontend
        image: cr.ttyuyin.com/itmp/itmp-frontend:latest
        ports:
        - containerPort: 80
        env:
        - name: DEPLOY_ENV
          value: "production"
        - name: API_URL
          value: "https://itmp.ttyuyin.com/api"
        - name: APP_URL
          value: "https://itmp.ttyuyin.com"
        - name: SSO_URL
          value: "ebc-sso.52tt.com"
        - name: CLIENT_ID
          value: "itmp"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 🔍 健康检查

所有环境都提供以下端点：
- `/health` - 健康检查
- `/env-info` - 环境信息
- `/config-info` - 配置信息（仅非生产环境）

CI/CD平台可以使用这些端点进行部署验证：
```bash
# 部署后验证
curl -f http://localhost/health
curl -s http://localhost/env-info | jq .environment
```

## 📝 注意事项

1. **镜像标签**: 建议使用Git commit SHA或构建号作为镜像标签
2. **环境变量**: 敏感信息通过CI/CD平台的密钥管理
3. **健康检查**: 部署后验证服务状态
4. **回滚策略**: 保留前几个版本的镜像用于快速回滚
5. **资源限制**: 生产环境设置适当的CPU和内存限制

## 🎯 核心优势

- ✅ **一次构建**: 同一镜像支持所有环境
- ✅ **配置分离**: 运行时环境变量配置
- ✅ **快速部署**: 无需重新构建
- ✅ **易于维护**: 统一的镜像管理
- ✅ **CI/CD友好**: 适配各种CI/CD平台
