# 🐳 ITMP Frontend - 运行时多环境 Docker 方案

## 🎯 方案概述

本方案实现了**一次构建，多环境部署**的Docker容器化方案，通过运行时环境变量来区分不同环境配置。

## ✨ 核心优势

- 🏗️ **一次构建**: 只需构建一个镜像，支持所有环境
- ⚡ **快速部署**: 无需重新构建，秒级切换环境
- 💾 **节省资源**: 减少镜像存储空间和构建时间
- 🔧 **灵活配置**: 运行时动态配置，支持环境变量覆盖
- 🔄 **蓝绿部署**: 支持零停机部署和快速回滚

## 🚀 快速开始

### 1. 构建镜像
```bash
# 构建通用镜像
./build-docker.sh v1.0.0

# 构建并推送到仓库
./build-docker.sh v1.0.0 true
```

### 2. 部署到不同环境
```bash
# 生产环境
./deploy.sh production v1.0.0

# 测试环境  
./deploy.sh test v1.0.0

# 开发环境
./deploy.sh development latest
```

### 3. 多环境测试
```bash
# 启动所有环境进行测试
docker-compose up -d

# 访问不同环境
# 开发: http://localhost:3001
# 测试: http://localhost:3002  
# 生产: http://localhost:3003
# 预发: http://localhost:3004
```

## 📋 环境配置

| 环境 | 端口 | API地址 | SSO地址 |
|------|------|---------|---------|
| development | 3001 | http://itmp-test.ttyuyin.com/api | test-ebc-sso.52tt.com |
| test | 3002 | http://itmp-test.ttyuyin.com/api | test-ebc-sso.52tt.com |
| staging | 3004 | http://staging.itmp.ttyuyin.com/api | test-ebc-sso.52tt.com |
| production | 80 | https://itmp.ttyuyin.com/api | ebc-sso.52tt.com |

## 🔧 工作原理

1. **构建阶段**: 使用 `pnpm build:docker` 构建包含占位符的前端应用
2. **运行时配置**: 容器启动时根据 `DEPLOY_ENV` 动态替换配置
3. **Nginx配置**: 根据环境变量生成不同的nginx配置
4. **健康检查**: 提供多个监控端点用于状态检查

## 📁 关键文件

```
├── Dockerfile                 # 多阶段构建文件
├── docker-entrypoint.sh      # 容器启动脚本
├── config-replacer.js        # 运行时配置替换脚本
├── nginx.conf.template       # Nginx配置模板
├── build-docker.sh          # 构建脚本
├── deploy.sh                # 部署脚本
├── docker-compose.yml       # 多环境编排文件
└── DOCKER-DEPLOYMENT.md     # 详细部署文档
```

## 🔍 监控端点

- `/health` - 健康检查
- `/env-info` - 环境信息
- `/config-info` - 配置信息（仅非生产环境）

## 📚 详细文档

查看 [DOCKER-DEPLOYMENT.md](./DOCKER-DEPLOYMENT.md) 获取完整的部署指南。

## 🎉 使用示例

```bash
# 构建镜像
./build-docker.sh v1.0.0

# 部署生产环境
./deploy.sh production v1.0.0

# 使用自定义API地址部署测试环境
OVERRIDE_API_URL=http://custom.api.com ./deploy.sh test v1.0.0

# 检查服务状态
curl http://localhost/health
curl http://localhost/env-info
```

这个方案完美解决了您的需求：**一次构建，运行时通过环境变量识别生产或开发环境**！
