# 🐳 ITMP Frontend - 运行时多环境 Docker 方案

## 🎯 方案概述

本方案实现了**一次构建，多环境部署**的Docker容器化方案，专为CI/CD平台设计，通过运行时环境变量来区分不同环境配置。

## ✨ 核心优势

- 🏗️ **一次构建**: 只需构建一个镜像，支持所有环境
- ⚡ **快速部署**: 无需重新构建，秒级切换环境
- 💾 **节省资源**: 减少镜像存储空间和构建时间
- 🤖 **CI/CD友好**: 适配各种CI/CD平台
- 🔧 **灵活配置**: 运行时动态配置，支持环境变量覆盖

## 🚀 CI/CD 平台使用

### 1. 构建镜像
```bash
# CI/CD平台构建命令
docker build -t cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG} .
```

### 2. 部署到不同环境
```bash
# 开发环境
docker run -d \
  -e DEPLOY_ENV=development \
  -e API_URL=http://itmp-test.ttyuyin.com/api \
  cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG}

# 生产环境
docker run -d \
  -e DEPLOY_ENV=production \
  -e API_URL=https://itmp.ttyuyin.com/api \
  cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG}
```

### 3. 本地测试（可选）
```bash
# 使用 Docker Compose 进行本地多环境测试
docker-compose up -d

# 访问不同环境
# 开发: http://localhost:3001
# 测试: http://localhost:3002
# 生产: http://localhost:3003
```

## 📋 环境配置

| 环境 | 端口 | API地址 | SSO地址 |
|------|------|---------|---------|
| development | 3001 | http://itmp-test.ttyuyin.com/api | test-ebc-sso.52tt.com |
| test | 3002 | http://itmp-test.ttyuyin.com/api | test-ebc-sso.52tt.com |
| staging | 3004 | http://staging.itmp.ttyuyin.com/api | test-ebc-sso.52tt.com |
| production | 80 | https://itmp.ttyuyin.com/api | ebc-sso.52tt.com |

## 🔧 工作原理

1. **构建阶段**: 使用 `pnpm build:docker` 构建包含占位符的前端应用
2. **运行时配置**: 容器启动时根据 `DEPLOY_ENV` 动态替换配置
3. **Nginx配置**: 根据环境变量生成不同的nginx配置
4. **健康检查**: 提供多个监控端点用于状态检查

## 📁 关键文件

```
├── Dockerfile                 # 多阶段构建文件
├── docker-entrypoint.sh      # 容器启动脚本
├── config-replacer.js        # 运行时配置替换脚本
├── nginx.conf.template       # Nginx配置模板
├── build-docker.sh          # 构建脚本
├── deploy.sh                # 部署脚本
├── docker-compose.yml       # 多环境编排文件
└── DOCKER-DEPLOYMENT.md     # 详细部署文档
```

## 🔍 监控端点

- `/health` - 健康检查
- `/env-info` - 环境信息
- `/config-info` - 配置信息（仅非生产环境）

## 📚 详细文档

查看 [DOCKER-DEPLOYMENT.md](./DOCKER-DEPLOYMENT.md) 获取完整的部署指南。

## 🎉 CI/CD 配置示例

### Jenkins Pipeline
```groovy
docker.build("${IMAGE_NAME}:${BUILD_TAG}")
docker.image("${IMAGE_NAME}:${BUILD_TAG}").push()

// 部署到测试环境
sh "docker run -d -e DEPLOY_ENV=test ${IMAGE_NAME}:${BUILD_TAG}"

// 部署到生产环境
sh "docker run -d -e DEPLOY_ENV=production ${IMAGE_NAME}:${BUILD_TAG}"
```

### Kubernetes
```yaml
env:
- name: DEPLOY_ENV
  value: "production"
- name: API_URL
  value: "https://itmp.ttyuyin.com/api"
```

### 健康检查
```bash
curl http://localhost/health
curl http://localhost/env-info
```

这个方案完美解决了您的需求：**一次构建，运行时通过环境变量识别生产或开发环境**！
