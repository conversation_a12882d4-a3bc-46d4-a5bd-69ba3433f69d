# ITMP Frontend 运行时多环境 Docker 部署指南

本项目采用**一次构建，多环境部署**的方案，通过运行时环境变量来区分不同环境的配置，专为CI/CD平台设计。

## ✨ 核心特性

- 🏗️ **一次构建**: 只需构建一个镜像，支持所有环境
- 🔧 **运行时配置**: 通过环境变量动态配置不同环境
- 🚀 **快速部署**: 无需重新构建，快速切换环境
- 📦 **镜像复用**: 减少存储空间和构建时间
- 🤖 **CI/CD友好**: 适配各种CI/CD平台

## 🚀 CI/CD 平台使用

### 1. 构建镜像

```bash
# CI/CD平台构建命令
docker build -t cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG} .
docker build -t cr.ttyuyin.com/itmp/itmp-frontend:latest .
```

### 2. 部署到不同环境

```bash
# 开发环境
docker run -d \
  --name itmp-frontend-dev \
  -p 3001:80 \
  -e DEPLOY_ENV=development \
  -e API_URL=http://itmp-test.ttyuyin.com/api \
  cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG}

# 生产环境
docker run -d \
  --name itmp-frontend-prod \
  -p 80:80 \
  -e DEPLOY_ENV=production \
  -e API_URL=https://itmp.ttyuyin.com/api \
  cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG}
```

### 3. 本地测试（可选）

```bash
# 使用 Docker Compose 进行本地多环境测试
docker-compose up -d

# 访问不同环境
# 开发: http://localhost:3001
# 测试: http://localhost:3002
# 生产: http://localhost:3003
```

## 📋 环境配置

### 支持的环境

| 环境 | DEPLOY_ENV | 默认端口 | 说明 |
|------|------------|----------|------|
| development | development | 3001 | 开发环境 |
| test | test | 3002 | 测试环境 |
| staging | staging | 3004 | 预发布环境 |
| production | production | 80 | 生产环境 |

### 运行时环境变量

#### 必需变量
- `DEPLOY_ENV`: 部署环境标识 (development/test/staging/production)

#### 可选变量
- `API_URL`: API服务地址
- `APP_URL`: 应用访问地址
- `SSO_URL`: SSO服务地址
- `CLIENT_ID`: 客户端ID
- `UPLOAD_URL`: 文件上传地址
- `API_PROXY_URL`: API代理地址（启用nginx代理）
- `APP_VERSION`: 应用版本号
- `BUILD_TIME`: 构建时间

### 环境变量覆盖

部署脚本支持通过环境变量覆盖默认配置：

```bash
# 使用自定义API地址
OVERRIDE_API_URL=http://custom.api.com ./deploy.sh production

# 使用自定义SSO地址
OVERRIDE_SSO_URL=custom-sso.com ./deploy.sh test

# 添加自定义环境变量
CUSTOM_ENV_VARS="-e CUSTOM_VAR=value" ./deploy.sh development
```

## 🔧 工作原理

### 1. 构建阶段
- 使用 `pnpm build:docker` 构建前端应用
- 构建产物包含占位符（如 `__vg_base_url`）
- 将所有环境配置文件打包到镜像中

### 2. 运行时配置替换
- 容器启动时运行 `config-replacer.js` 脚本
- 根据 `DEPLOY_ENV` 选择对应的环境配置
- 用环境变量值替换占位符
- 生成运行时配置文件 `_app.config.js`

### 3. Nginx 动态配置
- 使用 `nginx.conf.template` 模板
- 通过 `envsubst` 替换环境变量
- 根据环境调整缓存策略和日志级别

## 📦 镜像管理

### 构建脚本选项

```bash
./build-docker.sh [标签] [是否推送]
```

参数说明：
- `标签`: 镜像版本标签 [默认: latest]
- `是否推送`: true/false，是否推送到镜像仓库 [默认: false]

### 部署脚本选项

```bash
./deploy.sh [环境] [版本]
```

参数说明：
- `环境`: development/test/staging/production [默认: production]
- `版本`: 镜像版本标签 [默认: latest]

### 镜像标签规则

- `{registry}/{image}:{version}`: 版本标签
- `{registry}/{image}:latest`: 最新标签

示例：
- `cr.ttyuyin.com/itmp/itmp-frontend:v1.0.0`
- `cr.ttyuyin.com/itmp/itmp-frontend:latest`

## 🔍 监控和调试

### 健康检查端点

- `/health`: 基本健康检查
- `/env-info`: 环境信息（JSON格式）
- `/config-info`: 配置信息（仅非生产环境）

```bash
# 健康检查
curl http://localhost:8080/health

# 环境信息
curl http://localhost:8080/env-info

# 配置信息（开发/测试环境）
curl http://localhost:8080/config-info
```

### 查看容器日志

```bash
# Docker Compose
docker-compose logs -f itmp-frontend-prod

# 直接运行的容器
docker logs -f itmp-frontend-prod
```

### 进入容器调试

```bash
# 进入容器
docker exec -it itmp-frontend-prod /bin/bash

# 查看生成的配置
docker exec itmp-frontend-prod cat /usr/share/nginx/html/dist/_app.config.js

# 查看nginx配置
docker exec itmp-frontend-prod cat /etc/nginx/nginx.conf
```

## 🚀 生产环境部署

### 1. 构建和推送镜像

```bash
# 构建生产镜像
./build-docker.sh v1.0.0 true
```

### 2. 部署到生产服务器

```bash
# 方式1: 使用部署脚本
./deploy.sh production v1.0.0

# 方式2: 手动部署
docker pull cr.ttyuyin.com/itmp/itmp-frontend:v1.0.0
docker run -d \
  --name itmp-frontend \
  --restart unless-stopped \
  -p 80:80 \
  -e DEPLOY_ENV=production \
  -e API_URL=https://itmp.ttyuyin.com/api \
  -e APP_URL=https://itmp.ttyuyin.com \
  -e SSO_URL=ebc-sso.52tt.com \
  -e CLIENT_ID=itmp \
  cr.ttyuyin.com/itmp/itmp-frontend:v1.0.0
```

### 3. 使用 Docker Compose（推荐）

创建生产环境的 `docker-compose.prod.yml`：

```yaml
version: '3.8'
services:
  itmp-frontend:
    image: cr.ttyuyin.com/itmp/itmp-frontend:v1.0.0
    container_name: itmp-frontend
    ports:
      - "80:80"
    environment:
      - DEPLOY_ENV=production
      - API_URL=https://itmp.ttyuyin.com/api
      - APP_URL=https://itmp.ttyuyin.com
      - SSO_URL=ebc-sso.52tt.com
      - CLIENT_ID=itmp
    restart: unless-stopped
```

运行：
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🔄 CI/CD 集成

### GitHub Actions 示例

```yaml
name: Build and Deploy

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Build Docker Image
        run: |
          ./build-docker.sh ${{ github.ref_name }} true
          
  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Staging
        run: |
          ./deploy.sh staging ${{ github.ref_name }}
          
  deploy-production:
    needs: [build, deploy-staging]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to Production
        run: |
          ./deploy.sh production ${{ github.ref_name }}
```

## 📝 最佳实践

1. **版本管理**: 使用语义化版本号标记镜像
2. **环境隔离**: 不同环境使用不同的端口和配置
3. **健康检查**: 部署后验证服务状态
4. **日志监控**: 定期检查容器日志
5. **备份策略**: 部署前创建备份，支持快速回滚
6. **安全配置**: 生产环境禁用调试接口

## 📝 更新日志

- ✅ 支持运行时多环境配置
- ✅ 一次构建，多环境部署
- ✅ 动态 Nginx 配置
- ✅ API 代理支持
- ✅ 健康检查和监控
- ✅ 自动化构建和部署脚本
- ✅ 蓝绿部署和回滚支持
