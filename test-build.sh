#!/bin/bash

# 测试Docker构建和nginx配置脚本

set -e

echo "🏗️  开始测试Docker构建..."

# 构建镜像
docker build -t itmp-frontend-test:latest .

echo "✅ 构建成功！"

# 测试nginx配置生成
echo "🧪 测试nginx配置生成..."

# 创建临时容器测试配置
docker run --rm \
  -e DEPLOY_ENV=production \
  -e BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ") \
  -e APP_VERSION=test \
  itmp-frontend-test:latest \
  /bin/sh -c "
    # 生成nginx配置
    sed -e 's/\${DEPLOY_ENV}/production/g' \
        -e 's/\${BUILD_TIME}/2025-07-14T10:45:17Z/g' \
        -e 's/\${APP_VERSION}/test/g' \
        /etc/nginx/nginx.conf.template > /tmp/nginx.conf

    # 验证nginx配置
    nginx -t -c /tmp/nginx.conf
    echo '✅ Nginx配置验证通过'
  "

# 测试运行不同环境
echo "🧪 测试不同环境运行..."

# 测试生产环境
echo "启动生产环境容器..."
docker run -d \
  --name itmp-frontend-prod-test \
  -p 3003:80 \
  -e DEPLOY_ENV=production \
  -e API_URL=https://itmp.ttyuyin.com/api \
  -e SSO_URL=ebc-sso.52tt.com \
  -e CLIENT_ID=itmp \
  itmp-frontend-test:latest

# 等待容器启动
sleep 15

# 健康检查
echo "检查生产环境健康状态..."
if curl -f http://localhost:3003/health > /dev/null 2>&1; then
    echo "✅ 生产环境健康检查通过"
    echo "环境信息:"
    curl -s http://localhost:3003/env-info || echo "环境信息获取失败"
else
    echo "❌ 生产环境健康检查失败"
    echo "查看容器日志:"
    docker logs itmp-frontend-prod-test
fi

# 清理测试容器
echo "🧹 清理测试容器..."
docker stop itmp-frontend-prod-test || true
docker rm itmp-frontend-prod-test || true

echo "🎉 测试完成！"
