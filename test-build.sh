#!/bin/bash

# 测试Docker构建脚本

set -e

echo "🏗️  开始测试Docker构建..."

# 构建镜像
docker build -t itmp-frontend-test:latest .

echo "✅ 构建成功！"

# 测试运行不同环境
echo "🧪 测试不同环境运行..."

# 测试开发环境
echo "启动开发环境容器..."
docker run -d \
  --name itmp-frontend-dev-test \
  -p 3001:80 \
  -e DEPLOY_ENV=development \
  -e API_URL=http://itmp-test.ttyuyin.com/api \
  -e SSO_URL=test-ebc-sso.52tt.com \
  -e CLIENT_ID=itmp-dev \
  itmp-frontend-test:latest

# 等待容器启动
sleep 10

# 健康检查
echo "检查开发环境健康状态..."
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "✅ 开发环境健康检查通过"
    curl -s http://localhost:3001/env-info | jq . || echo "环境信息获取成功"
else
    echo "❌ 开发环境健康检查失败"
fi

# 清理测试容器
echo "🧹 清理测试容器..."
docker stop itmp-frontend-dev-test || true
docker rm itmp-frontend-dev-test || true

echo "🎉 测试完成！"
