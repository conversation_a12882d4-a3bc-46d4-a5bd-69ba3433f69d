version: '3.8'

services:
  # 开发环境
  itmp-frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
    image: itmp-frontend:latest
    container_name: itmp-frontend-dev
    ports:
      - "3001:80"
    environment:
      - DEPLOY_ENV=development
      - APP_VERSION=${APP_VERSION:-latest}
      - BUILD_TIME=${BUILD_TIME:-}
      - API_URL=http://itmp-test.ttyuyin.com/api
      - APP_URL=http://localhost:3001
      - SSO_URL=test-ebc-sso.52tt.com
      - CLIENT_ID=itmp-dev
      - UPLOAD_URL=/upload
      - API_PROXY_URL=http://itmp-test.ttyuyin.com/api
    restart: unless-stopped
    networks:
      - itmp-network

  # 测试环境
  itmp-frontend-test:
    image: itmp-frontend:latest
    container_name: itmp-frontend-test
    ports:
      - "3002:80"
    environment:
      - DEPLOY_ENV=test
      - APP_VERSION=${APP_VERSION:-latest}
      - BUILD_TIME=${BUILD_TIME:-}
      - API_URL=http://itmp-test.ttyuyin.com/api
      - APP_URL=http://test.itmp.ttyuyin.com
      - SSO_URL=test-ebc-sso.52tt.com
      - CLIENT_ID=itmp-test
      - UPLOAD_URL=/upload
      - API_PROXY_URL=http://itmp-test.ttyuyin.com/api
    restart: unless-stopped
    networks:
      - itmp-network
    depends_on:
      - itmp-frontend-dev

  # 生产环境
  itmp-frontend-prod:
    image: itmp-frontend:latest
    container_name: itmp-frontend-prod
    ports:
      - "3003:80"
    environment:
      - DEPLOY_ENV=production
      - APP_VERSION=${APP_VERSION:-latest}
      - BUILD_TIME=${BUILD_TIME:-}
      - API_URL=https://itmp.ttyuyin.com/api
      - APP_URL=https://itmp.ttyuyin.com
      - SSO_URL=ebc-sso.52tt.com
      - CLIENT_ID=itmp
      - UPLOAD_URL=/upload
      - API_PROXY_URL=https://itmp.ttyuyin.com/api
    restart: unless-stopped
    networks:
      - itmp-network
    depends_on:
      - itmp-frontend-dev

  # Staging环境（使用测试配置）
  itmp-frontend-staging:
    image: itmp-frontend:latest
    container_name: itmp-frontend-staging
    ports:
      - "3004:80"
    environment:
      - DEPLOY_ENV=staging
      - APP_VERSION=${APP_VERSION:-latest}
      - BUILD_TIME=${BUILD_TIME:-}
      - API_URL=http://staging.itmp.ttyuyin.com/api
      - APP_URL=http://staging.itmp.ttyuyin.com
      - SSO_URL=test-ebc-sso.52tt.com
      - CLIENT_ID=itmp-staging
      - UPLOAD_URL=/upload
      - API_PROXY_URL=http://staging.itmp.ttyuyin.com/api
    restart: unless-stopped
    networks:
      - itmp-network
    depends_on:
      - itmp-frontend-dev

networks:
  itmp-network:
    driver: bridge
